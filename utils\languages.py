"""
Language support for the bot
"""

# Messages in different languages
MESSAGES = {
    'tm': {  # Turkmen
        'welcome': "TmM CELL BOT ulgamyna hoş geldiňiz! 👀 \n\nHyzmaty telefon belgisi boýunça maglumatlary tapmak boýunça alyp barýar we tapyş üçin açyk we jemgyýetçilik bazalaryny ulanýar. \n\nServis hakyky wagt düzgüninde işleýär we alnan ähli maglumatlary saklamazdan hasabat emele getirýär edýär \n\nTehniki kömek - @TMCELLadmin",
        'language_select': "Please select your language / Пожалуйста, выберите язык:",
        'phone_confirm': "🗂 Gözleg funksiýasyny ulanmak üçin telefon belgiňizi tassyklamaly.\n\nTassyklamak üçin aşakdaky düwmä basyň.",
        'phone_button': "📲 Telefon belgiňizi iberiň",
        'phone_success': "Telefon belgiňiz üstünlikli tassyklandy! Indi gözleg hyzmatyndan peýdalanyp bilersiňiz.",
        'phone_verification_failed': "⚠️ Telefon belgiňiz tassyklanmady. Gaýtadan synanyşyň.",
        'search_start_verification': "🔍 GÖZLEMÄGE BAŞLA",
        'search_options': "📱 Gözleg görnüşini saýlaň:\n\n📱 Telefon belgisi boýunça\n👤 Ady boýunça\n📗 Passport boýunça",
        'search_form': "✅ 993 we 63 bilen başlanýan belgileriň gözlegi üçin forma!\n\n🔍Şerti mugt gözlegler ulanylýar \n* Sizde {searches_left} mugt ballar galdy.\n\n⚠️ +993 bilen başlanýan belgiler gözlenmeýär!\n\n🙈 Telefon belgiňizi gözleg üçin giriziň:",
        'search_name_form': "✅ Ady boýunça gözleg üçin forma!\n\n🔍Şerti mugt gözlegler ulanylýar \n* Sizde {searches_left} mugt ballar galdy.\n\n⚠️ Azyndan 3 harp giriziň!\n\n👤 Gözlemek isleýän adyňyzy giriziň:",
        'search_passport_form': "✅ Passport boýunça gözleg üçin forma!\n\n🔍Şerti mugt gözlegler ulanylýar \n* Sizde {searches_left} mugt ballar galdy.\n\n⚠️ Azyndan 4 simwol giriziň!\n\n📗 Passport belgisini giriziň:",
        'invalid_name_query': "⚠️ Ady gözlemek üçin azyndan 3 harp giriziň.",
        'invalid_passport_query': "⚠️ Passport gözlemek üçin azyndan 4 simwol giriziň.",
        'cancel': "❌️ OTMENA",
        'searching': "📡📡📡 Belgi barada maglumat gözlenýär {phone_number}...",
        'search_results': "✅ Gözleg tamamlandy!\n🔍 1 sany netije tapyldy\n\n📱 Nomeri:\n<code>{phone}</code>\n\n👤 Ady:\n<code>{name}</code>\n\n🏠 Adresi:\n<code>{address}</code>\n\n📗 Passport:\n<code>{passport}</code>\n\n📅 Doglan ýeri we senesi:\n<code>{birth_info}</code>\n\n🆔 SIM ID:\n<code>{sim_id}</code>\n\n🤖 Bot: @tmcell993bot",
        'no_results': "Bagyşlaň, şeýle bir nomer tapylmady.",
        'limit_reached': "Siz şu gün üçin zaproslar çägine ýetdiňiz. 😓",
        'no_points': "Siziň gözleg ballaryňyz gutardy. 😓",
        'profile_title': "Hasabyňyza hoş geldiňiz {username} 👋",
        'profile_info': "🆔 ID: {user_id}\n📊 Jemi gözlegler: {total_searches}\n👥 Çagyrylan ulanyjylar: {referrals}\n\n🔍 Gözleg ballary: {search_points}\n* Her gözleg üçin 1 bal harçlanýar.\n\nUlanyjylary çagyryň we gözleg ballary gazanyň! Her çagyrylan ulanyjy üçin +1 gözleg baly.\n✅ Siziň referal link:\n{referral_link}",
        'profile_info_extended': "🆔 ID: {user_id}\n📊 Jemi gözlegler: {total_searches}\n📱 Soňky gözlegler: {recent_searches}\n👥 Çagyrylan ulanyjylar: {referrals}\n\n📲 Telefon belgisi: {phone_number}\n📱 Telefon tassyklanan: {phone_verified}\n\n👑 VIP Status: {vip_status}\n🔍 Gözleg ballary: {search_points}\n* Her gözleg üçin 1 bal harçlanýar.\n\nUlanyjylary çagyryň we gözleg ballary gazanyň! Her çagyrylan ulanyjy üçin +1 gözleg baly.\n✅ Siziň referal link:\n{referral_link}",
        'vip_status_active': "✅ Aktiw ({days} gün galdy)",
        'vip_status_expired': "❌ Möhleti gutardy",
        'vip_status_active_no_expiry': "✅ Aktiw (möhletsiz)",
        'vip_status_inactive': "❌ Aktiw däl",
        'back': "⬅️ Yza",
        'vip_title': "Botymyzyň VIP tölegli aýratynlyklary:",
        'vip_tmcell': "📱 TMCELL (Töleg)",
        'vip_dealer': "TMCELL arkaly töleg üçin diler saýlaň:\n\nTmM CELL Bot-yň resmi eýeleri we administratorlary:",
        'vip_owner': "👤 BOT EÝESİ",
        'faq_title': "Köp soralýan soraglar (FAQ)",
        'faq_content': (
            "<b>Baglan Gözleg Bot — Ulanyjy Kömek we Sorag-Jogaplar</b>\n\n"
            "<b>1. Botdan nähili peýdalanyp bilerin?</b>\n"
            "— Başlamak üçin /start ýa-da 'Gözleg' düwmesine basyň. Telefon belgiňizi tassyklap, gözleg görnüşini saýlaň.\n\n"
            "<b>2. Gözlegler nähili işleýär?</b>\n"
            "— Telefon belgisi, ady ýa-da passport boýunça maglumat gözläp bilersiňiz. Netije tapylanda maglumatlar ekranda görkeziler.\n\n"
            "<b>3. Gözleg çäklendirmeleri barmy?</b>\n"
            "— Her ulanyjy her gün 1 mugt gözleg edip biler. Köp gözleg üçin VIP ýa-da goşmaça ballar gerek.\n\n"
            "<b>4. Ýalňyşlyk ýa-da tehniki mesele ýüze çyksa näme etmeli?</b>\n"
            "— Ilki internetiňizi barlaň. Problemalar dowam etse, @baglan_support bilen habarlaşyň.\n\n"
            "<b>5. Netije tapylmasa näme etmeli?</b>\n"
            "— Eger maglumat tapylmasa, bu belgide maglumat ýoklugyny aňladýar. Başga maglumat ýa-da başga belgi synap görüň.\n\n"
            "<b>6. Profil we sazlamalar</b>\n"
            "— Profil bölümini açyp, maglumatlaryňyzy we sazlamalaryňyzy üýtgedip bilersiňiz.\n\n"
            "<b>7. VIP statusy näme berýär?</b>\n"
            "— VIP ulanyjylar çäksiz gözleg, öňdebaryjy hyzmat we aýratyn mümkinçilikler alýarlar.\n\n"
            "<b>8. Dostlarymy nädip çagyrsam bolýar?</b>\n"
            "— Profilde referal linkiňizi tapyp, dostlaryňyza paýlaşyň. Her çagyrylan ulanyjy üçin bonus bal alarsyňyz.\n\n"
            "<b>9. Maglumatlary nädip eksport edip bilerin?</b>\n"
            "— Gözleg taryhyňyzy eksport etmek üçin degişli düwmä basyň. CSV ýa-da QR görnüşinde alyp bilersiňiz.\n\n"
            "<b>10. Has giňişleýin kömek gerekmi?</b>\n"
            "— Islendik sorag üçin @baglan_support bilen habarlaşyň!\n"
        ),
        'change_language': "🇬🇧 Dili üýtget",
        'select_new_language': "Выберите новый язык:",
        'search_button': "GÖZLEMÄGE BAŞLA",
        'profile_button': "Şahsy Otag",
        'profile': "Profil",
        'vip_button': "VIP SATYN AL",
        'faq_button': "Sorag",
        'language_button': "Dili üýtget",
        'filter_button': "Filtrleme",
        'filter_title': "Gözleg netijeleri üçin filtrleri saýlaň:",
        'filter_by_city': "Şäher boýunça filtrle",
        'filter_by_year': "Doglan ýyly boýunça filtrle",
        'filter_by_name': "Ady boýunça filtrle",
        'filter_reset': "Filtrleri arassala",
        'interactive_view': "Jikme-jik gör",
        'interactive_share': "Paýlaş",
        'interactive_save': "Belliklere goş",
        'interactive_export': "Eksport et",
        'combined_search_prompt': "✅ Kombinirlenýän gözleg üçin forma!\n\n🔍Şerti mugt gözlegler ulanylýar \n* Sizde {searches_left} mugt ballar galdy.\n\n⚠️ Telefon belgisi, ady ýa-da passport belgisini giriziň!\n\n🔍 Gözlemek isleýän maglumaty giriziň:",
        'export_caption': "📥 Gözleg netijesi eksport edildi",
        'city_filter_prompt': "Şäheri giriziň (mysal: Aşgabat):",
        'year_filter_prompt': "Doglan ýyly giriziň (mysal: 1990):",
        'name_filter_prompt': "Ady giriziň (mysal: Aman):",
        'history_button': "Gözleg taryhy",
        'favorites_button': "Bellikler",
        'history_title': "Siň gözleg taryhyňyz:",
        'favorites_title': "Siň belliklerňiz:",
        'no_history': "Gözleg taryhyňyz boş.",
        'no_favorites': "Belliklerňiz boş.",
        'favorite_added': "Netije belliklere goşuldy!",
        'favorite_exists': "Bu netije eýýäm belliklerde bar.",
        'delete_favorite': "Bellikleri ýok et",
        'clear_history': "Taryhy arassala",
        'combined_search': "Kombinirleýin gözleg",
        'combined_search_prompt': "Birnäçe parametrleri giriziň (mysal: Ady we pasport belgisi):",
        'theme_button': "Temany üýtget",
        'theme_light': "Açyk tema",
        'theme_dark': "Garaňky tema",
        'theme_changed': "Tema üýtgedildi",
        'current_theme': "Häzirki tema",
        'theme_auto': "Telegram bilen sinhronizirle",
        'theme_auto_desc': "Tema Telegram sazlamalary bilen awtomatiki sinhronizirlenýär",
        'search_again': "Başga gözleg geçirmek isleseňiz, aşakdaky düwmä basyň:",
        'phone_verification_required': "Gözleg funksiýasyny ulanmak üçin telefon belgiňizi tassyklamaly.",
        'phone_verification_button': "Telefon belgiňizi iberiň",
        'phone_verification_success': "Telefon belgiňiz üstünlikli tassyklandy! Indi baglan nomer gözleg hyzmatyndan peýdalanyp bilersiňiz.",
        'settings_button': "Sazlamalar",
        'notifications_button': "Bildirişler",
        'support_button': "Tehniki kömek",
        'commands_button': "Komandalar",
        'support_title': "Tehniki kömek",
        'support_description': "Tehniki kömek üçin @TMCELLadmin bilen habarlaşyp bilersiňiz.",
        'support_contact': "Habarlaşmak",
        'notification_new': "🆕 Täze bildiriş!",
        'notification_read': "Bildiriş okaldy",
        'notification_mark_all': "Hemmesini okaldy diýip bellikle",
        'notification_none': "Siziň bildirişiňiz ýok",
        'notification_unread': "Okalmedik bildirişler: {count}",
        'notification_create': "Bildiriş döret",
        'notification_delete': "Bildirişi poz",
        'notification_type_feature': "Täze funksiýa",
        'notification_type_system': "Ulgam bildirişi",
        'notification_type_promo': "Promo bildiriş",
        'settings_title': "Sazlamalar",
        'language_setting': "Dil",
        'theme_setting': "Tema",
        'notification_settings': "Bildiriş Sazlamalary",
        'notifications_enabled': "Açyk",
        'notifications_disabled': "Ýapyk",
        'notification_status': "Bildiriş Ýagdaýy",
        'enable_notifications': "Bildirişleri Aç",
        'disable_notifications': "Bildirişleri Ýap",
        'notifications_now_enabled': "Bildirişler açyldy",
        'notifications_now_disabled': "Bildirişler ýapyldy",

        # Top users
        'top_points': "Iň köp baly bar ulanyjylar",
        'top_referrals': "Iň köp ulanyjy çagyranlar",
        'refresh': "Täzelemek",

        # User profile
        'edit_profile': "Profili üýtget",
        'search_preferences': "Gözleg sazlamalary",
        'edit_name': "Adyňy üýtget",
        'edit_email': "Email üýtget",
        'edit_phone': "Telefon üýtget",
        'edit_bio': "Özüň barada üýtget",
        'upload_photo': "Surat ýükle",
        'enter_new_name': "Täze adyňyzy giriziň:",
        'enter_new_email': "Täze email adresiňizi giriziň:",
        'enter_new_phone': "Täze telefon belgiňizi giriziň:",
        'enter_new_bio': "Özüňiz barada maglumaty giriziň:",
        'upload_new_photo': "Profil suraty hökmünde goýmak üçin surat iberiň.",
        'name_updated': "✅ Adyňyz üýtgedildi!",
        'email_updated': "✅ Email adresiňiz üýtgedildi!",
        'phone_updated': "✅ Telefon belgiňiz üýtgedildi!",
        'bio_updated': "✅ Özüňiz barada maglumat üýtgedildi!",
        'photo_updated': "✅ Profil suratyňyz üýtgedildi!",
        'invalid_email': "❌ Nädogry email formaty. Gaýtadan synanyşyň.",
        'invalid_phone': "❌ Nädogry telefon belgisi. Dogry belgini giriziň.",
        'no_photo_found': "❌ Surat tapylmady. Surat iberiň.",
        'not_set': "Görkezilmedi",
        'edit_profile_text': "<b>✏️ Profili üýtgetmek</b>\n\nNämäni üýtgetmek isleýäniňizi saýlaň:",

        # Search preferences
        'set_results_per_page': "Sahypada netije sanyny bellemek",
        'set_sort_order': "Tertip görnüşini bellemek",
        'set_default_type': "Adaty gözleg görnüşini bellemek",
        'select_results_per_page': "Sahypada näçe netije görkezmeli:",
        'select_sort_order': "Gözleg netijeleri üçin tertip görnüşini saýlaň:",
        'select_default_type': "Adaty gözleg görnüşini saýlaň:",
        'sort_newest': "Ilki täzeler",
        'sort_oldest': "Ilki köneler",
        'search_type_phone': "Telefon belgisi",
        'search_type_name': "Ady",
        'search_type_passport': "Pasport",
        'results_per_page_updated': "Sahypada netije sany üýtgedildi!",
        'sort_order_updated': "Tertip görnüşi üýtgedildi!",
        'default_type_updated': "Adaty gözleg görnüşi üýtgedildi!",

        # Voice search
        'voice_search': "🎙 Ses bilen gözleg",
        'voice_search_button': "🎙 Ses bilen gözleg",
        'voice_search_prompt': "Gözlemek üçin telefon belgisi ýa-da at bilen ses habaryny iberiň.",
        'voice_processing': "🔍 Siziň ses habaryňyz işlenýär...",
        'voice_recognized': "Tanalýan tekst",
        'voice_not_understood': "❌ Bagyşlaň, men siziň ses habaryňyzy düşünip bilmedim. Gaýtadan synanyşyň.",
        'voice_api_error': "❌ Ses tanaýyş hyzmaty bilen baglanyşykda ýalňyşlyk",
        'voice_error': "❌ Ses habary işlemekde ýalňyşlyk",
        'voice_tips': "📝 <b>Ses bilen gözleg maslahatlar:</b>\n\n• Telefon belgisini anyk aýdyň: '993 65 123 45 67'\n• Ady doly aýdyň: 'Serdar Berdimuhamedow'\n• Ümsüm ýerde gürleşiň\n• Mikrofona ýakyn duruň\n• Sözleri haýal we anyk aýdyň",
        'voice_help': "🎙 <b>Ses bilen gözleg kömegi</b>\n\nSes bilen gözleg etmek üçin:\n1. Mikrofon düwmesini basyp saklaň\n2. Telefon belgisini ýa-da ady anyk aýdyň\n3. Düwmäni goýberiň\n\nMysal: '993 65 123 45 67' ýa-da 'Serdar Berdimuhamedow'",
        'notes_button': "📝 Bellikler",

        # Security messages
        'too_fast': "⚠️ <b>Gaty çalt haýyşlar!</b>\n\nGözlegler arasynda biraz garaşyň.",
        'unsafe_input': "⚠️ <b>Potensial howply giriş anyklandy.</b>\n\nDiňe harplar, sanlar we adaty nyşanlar ulanyň.",
        'security_warning': "🔒 <b>Howpsuzlyk duýduryşy</b>\n\nSiziň hasabyňyzda şübheli hereket anyklandy. Hasabyňyzy goramak üçin parolyňyzy üýtgediň.",
        'account_locked': "🔒 <b>Hasap wagtlaýyn blokirlendi</b>\n\nBirnäçe şübheli hereket sebäpli, siziň hasabyňyz howpsuzlyk maksady bilen wagtlaýyn blokirlendi. 30 minutdan soň gaýtadan synanyşyň.",
        'security_check': "🔍 <b>Howpsuzlyk barlagy</b>\n\nBu hereket howpsuzlyk barlagyny talap edýär. Tassyklamak üçin telefon belgiňizi tassyklaň."
    },
    'ru': {  # Russian
        'welcome': "Добро пожаловать в систему поиска TmM CELL! 👀 \n\nСервис предоставляет информацию по номеру телефона, используя открытые и общедоступные базы данных. \n\nСервис работает в режиме реального времени и формирует отчет без сохранения всей полученной информации. \n\nТехническая поддержка - @TMCELLadmin",
        'language_select': "Please select your language / Пожалуйста, выберите язык:",
        'phone_confirm': "🗂 Вам необходимо подтвердить свой номер телефона для завершения идентификации и использования поиска.\n\nНажмите кнопку ниже для подтверждения.",
        'phone_button': "📲 Отправить номер телефона",
        'phone_success': "Ваш номер телефона успешно подтвержден! Теперь вы можете пользоваться услугой поиска.",
        'phone_verification_failed': "⚠️ Ваш номер телефона не подтвержден. Пожалуйста, попробуйте снова.",
        'search_start_verification': "🔍 НАЧАТЬ ПОИСК",
        'search_options': "📱 Выберите тип поиска:\n\n📱 По номеру телефона\n👤 По имени\n📗 По паспорту",
        'search_form': "✅ Форма для поиска номеров, начинающихся с 993 и 63!\n\n🔍Используются бесплатные поиски \n* У вас осталось {searches_left} бесплатных баллов.\n\n⚠️ Номера, начинающиеся с +993, не будут искаться!\n\n🙈 Введите номер телефона для поиска:",
        'search_name_form': "✅ Форма для поиска по имени!\n\n🔍Используются бесплатные поиски \n* У вас осталось {searches_left} бесплатных баллов.\n\n⚠️ Введите минимум 3 буквы!\n\n👤 Введите имя для поиска:",
        'search_passport_form': "✅ Форма для поиска по паспорту!\n\n🔍Используются бесплатные поиски \n* У вас осталось {searches_left} бесплатных баллов.\n\n⚠️ Введите минимум 4 символа!\n\n📗 Введите номер паспорта:",
        'invalid_name_query': "⚠️ Для поиска по имени введите минимум 3 буквы.",
        'invalid_passport_query': "⚠️ Для поиска по паспорту введите минимум 4 символа.",
        'cancel': "❌️ ОТМЕНА",
        'searching': "📡📡📡 Поиск информации о номере {phone_number}...",
        'search_results': "✅ Поиск завершен!\n🔍 Найден 1 результат\n\n📱 Номер:\n<code>{phone}</code>\n\n👤 ФИО:\n<code>{name}</code>\n\n🏠 Адрес:\n<code>{address}</code>\n\n📗 Паспорт:\n<code>{passport}</code>\n\n📅 Место и дата рождения:\n<code>{birth_info}</code>\n\n🆔 SIM ID:\n<code>{sim_id}</code>\n\n🤖 Бот: @tmcell993bot",
        'no_results': "Извините, такой номер не найден.",
        'limit_reached': "Вы достигли лимита запросов на сегодня. 😓",
        'no_points': "У вас закончились баллы поиска. 😓",
        'profile_title': "Добро пожаловать в ваш аккаунт {username} 👋",
        'profile_info': "🆔 ID: {user_id}\n📊 Всего поисков: {total_searches}\n👥 Приглашенные пользователи: {referrals}\n\n🔍 Баллы поиска: {search_points}\n* За каждый поиск расходуется 1 балл.\n\nПриглашайте пользователей и получайте баллы поиска! За каждого приглашенного пользователя +1 балл поиска.\n✅ Ваша реферальная ссылка:\n{referral_link}",
        'profile_info_extended': "🆔 ID: {user_id}\n📊 Всего поисков: {total_searches}\n📱 Недавние поиски: {recent_searches}\n👥 Приглашенные пользователи: {referrals}\n\n📲 Номер телефона: {phone_number}\n📱 Телефон подтвержден: {phone_verified}\n\n👑 VIP Статус: {vip_status}\n🔍 Баллы поиска: {search_points}\n* За каждый поиск расходуется 1 балл.\n\nПриглашайте пользователей и получайте баллы поиска! За каждого приглашенного пользователя +1 балл поиска.\n✅ Ваша реферальная ссылка:\n{referral_link}",
        'vip_status_active': "✅ Активен (осталось {days} дней)",
        'vip_status_expired': "❌ Срок истек",
        'vip_status_active_no_expiry': "✅ Активен (бессрочно)",
        'vip_status_inactive': "❌ Неактивен",
        'back': "⬅️ Назад",
        'vip_title': "Платные VIP-функции нашего бота:",
        'vip_tmcell': "📱 TMCELL (Оплата)",
        'vip_dealer': "Выберите дилера для оплаты через TMCELL:\n\nОфициальные владельцы и администраторы TmM CELL Bot:",
        'vip_owner': "👤 ВЛАДЕЛЕЦ БОТА",
        'faq_title': "Часто задаваемые вопросы (FAQ)",
        'faq_content': (
            "<b>Baglan Gözleg Bot — Ulanyjy Kömek we Sorag-Jogaplar</b>\n\n"
            "<b>1. Botdan nähili peýdalanyp bilerin?</b>\n"
            "— Başlamak üçin /start ýa-da 'Gözleg' düwmesine basyň. Telefon belgiňizi tassyklap, gözleg görnüşini saýlaň.\n\n"
            "<b>2. Gözlegler nähili işleýär?</b>\n"
            "— Telefon belgisi, ady ýa-da passport boýunça maglumat gözläp bilersiňiz. Netije tapylanda maglumatlar ekranda görkeziler.\n\n"
            "<b>3. Gözleg çäklendirmeleri barmy?</b>\n"
            "— Her ulanyjy her gün 1 mugt gözleg edip biler. Köp gözleg üçin VIP ýa-da goşmaça ballar gerek.\n\n"
            "<b>4. Ýalňyşlyk ýa-da tehniki mesele ýüze çyksa näme etmeli?</b>\n"
            "— Ilki internetiňizi barlaň. Problemalar dowam etse, @baglan_support bilen habarlaşyň.\n\n"
            "<b>5. Netije tapylmasa näme etmeli?</b>\n"
            "— Eger maglumat tapylmasa, bu belgide maglumat ýoklugyny aňladýar. Başga maglumat ýa-da başga belgi synap görüň.\n\n"
            "<b>6. Profil we sazlamalar</b>\n"
            "— Profil bölümini açyp, maglumatlaryňyzy we sazlamalaryňyzy üýtgedip bilersiňiz.\n\n"
            "<b>7. VIP statusy näme berýär?</b>\n"
            "— VIP ulanyjylar çäksiz gözleg, öňdebaryjy hyzmat we aýratyn mümkinçilikler alýarlar.\n\n"
            "<b>8. Dostlarymy nädip çagyrsam bolýar?</b>\n"
            "— Profilde referal linkiňizi tapyp, dostlaryňyza paýlaşyň. Her çagyrylan ulanyjy üçin bonus bal alarsyňyz.\n\n"
            "<b>9. Maglumatlary nädip eksport edip bilerin?</b>\n"
            "— Gözleg taryhyňyzy eksport etmek üçin degişli düwmä basyň. CSV ýa-da QR görnüşinde alyp bilersiňiz.\n\n"
            "<b>10. Has giňişleýin kömek gerekmi?</b>\n"
            "— Islendik sorag üçin @baglan_support bilen habarlaşyň!\n"
        ),
        'change_language': "🇷🇺 Изменить язык",
        'select_new_language': "Выберите новый язык:",
        'search_button': "НАЧАТЬ ПОИСК",
        'profile_button': "Личный кабинет",
        'profile': "Профиль",
        'vip_button': "КУПИТЬ VIP",
        'faq_button': "Вопросы",
        'language_button': "Изменить язык",
        'filter_button': "Фильтрация",
        'filter_title': "Выберите фильтры для результатов поиска:",
        'filter_by_city': "Фильтр по городу",
        'filter_by_year': "Фильтр по году рождения",
        'filter_by_name': "Фильтр по имени",
        'filter_reset': "Сбросить фильтры",
        'interactive_view': "Подробнее",
        'interactive_share': "Поделиться",
        'interactive_save': "Добавить в избранное",
        'interactive_export': "Экспорт",
        'combined_search_prompt': "✅ Форма для комбинированного поиска!\n\n🔍Используются бесплатные поиски \n* У вас осталось {searches_left} бесплатных баллов.\n\n⚠️ Введите номер телефона, имя или номер паспорта!\n\n🔍 Введите информацию для поиска:",
        'export_caption': "📥 Результат поиска экспортирован",
        'city_filter_prompt': "Введите город (например: Ашхабад):",
        'year_filter_prompt': "Введите год рождения (например: 1990):",
        'name_filter_prompt': "Введите имя (например: Аман):",
        'history_button': "История поиска",
        'favorites_button': "Избранное",
        'history_title': "Ваша история поиска:",
        'favorites_title': "Ваши избранные результаты:",
        'no_history': "История поиска пуста.",
        'no_favorites': "У вас нет избранных результатов.",
        'favorite_added': "Результат добавлен в избранное!",
        'favorite_exists': "Этот результат уже в избранном.",
        'delete_favorite': "Удалить из избранного",
        'clear_history': "Очистить историю",
        'combined_search': "Комбинированный поиск",
        'combined_search_prompt': "Введите несколько параметров (например: имя и номер паспорта):",
        'theme_button': "Изменить тему",
        'theme_light': "Светлая тема",
        'theme_dark': "Тёмная тема",
        'theme_changed': "Тема изменена",
        'current_theme': "Текущая тема",
        'theme_auto': "Синхронизировать с Telegram",
        'theme_auto_desc': "Тема автоматически синхронизируется с настройками Telegram",
        'search_again': "Чтобы выполнить другой поиск, нажмите кнопку ниже:",
        'phone_verification_required': "Для использования функции поиска необходимо подтвердить ваш номер телефона.",
        'phone_verification_button': "Отправить номер телефона",
        'phone_verification_success': "Ваш номер телефона успешно подтвержден! Теперь вы можете использовать сервис поиска номеров tmcell.",
        'settings_button': "Настройки",
        'notifications_button': "Уведомления",
        'support_button': "Техническая поддержка",
        'commands_button': "Команды",
        'support_title': "Техническая поддержка",
        'support_description': "Для технической поддержки вы можете связаться с @TMCELLadmin.",
        'support_contact': "Связаться",
        'notification_new': "🆕 Новое уведомление!",
        'notification_read': "Уведомление прочитано",
        'notification_mark_all': "Отметить все как прочитанные",
        'notification_none': "У вас нет уведомлений",
        'notification_unread': "Непрочитанных уведомлений: {count}",
        'notification_create': "Создать уведомление",
        'notification_delete': "Удалить уведомление",
        'notification_type_feature': "Новая функция",
        'notification_type_system': "Системное уведомление",
        'notification_type_promo': "Промо уведомление",
        'settings_title': "Настройки",
        'language_setting': "Язык",
        'theme_setting': "Тема",
        'notification_settings': "Настройки уведомлений",
        'notifications_enabled': "Включены",
        'notifications_disabled': "Отключены",
        'notification_status': "Статус уведомлений",
        'enable_notifications': "Включить уведомления",
        'disable_notifications': "Отключить уведомления",
        'notifications_now_enabled': "Уведомления включены",
        'notifications_now_disabled': "Уведомления отключены",

        # Top users
        'top_points': "Топ пользователей по баллам",
        'top_referrals': "Топ пользователей по рефералам",
        'refresh': "Обновить",

        # User profile
        'edit_profile': "Редактировать профиль",
        'search_preferences': "Настройки поиска",
        'edit_name': "Изменить имя",
        'edit_email': "Изменить email",
        'edit_phone': "Изменить телефон",
        'edit_bio': "Изменить о себе",
        'upload_photo': "Загрузить фото",
        'enter_new_name': "Пожалуйста, введите ваше новое имя:",
        'enter_new_email': "Пожалуйста, введите ваш новый email адрес:",
        'enter_new_phone': "Пожалуйста, введите ваш новый номер телефона:",
        'enter_new_bio': "Пожалуйста, введите информацию о себе:",
        'upload_new_photo': "Пожалуйста, отправьте фото для установки в качестве фото профиля.",
        'name_updated': "✅ Ваше имя обновлено!",
        'email_updated': "✅ Ваш email обновлен!",
        'phone_updated': "✅ Ваш номер телефона обновлен!",
        'bio_updated': "✅ Информация о вас обновлена!",
        'photo_updated': "✅ Ваше фото профиля обновлено!",
        'invalid_email': "❌ Неверный формат email. Пожалуйста, попробуйте снова.",
        'invalid_phone': "❌ Неверный номер телефона. Пожалуйста, введите корректный номер.",
        'no_photo_found': "❌ Фото не найдено. Пожалуйста, отправьте фото.",
        'not_set': "Не указано",
        'edit_profile_text': "<b>✏️ Редактирование профиля</b>\n\nВыберите, что вы хотите изменить:",

        # Search preferences
        'set_results_per_page': "Установить количество результатов",
        'set_sort_order': "Установить порядок сортировки",
        'set_default_type': "Установить тип поиска по умолчанию",
        'select_results_per_page': "Выберите, сколько результатов показывать на странице:",
        'select_sort_order': "Выберите порядок сортировки результатов поиска:",
        'select_default_type': "Выберите тип поиска по умолчанию:",
        'sort_newest': "Сначала новые",
        'sort_oldest': "Сначала старые",
        'search_type_phone': "Номер телефона",
        'search_type_name': "Имя",
        'search_type_passport': "Паспорт",
        'results_per_page_updated': "Количество результатов на странице обновлено!",
        'sort_order_updated': "Порядок сортировки обновлен!",
        'default_type_updated': "Тип поиска по умолчанию обновлен!",

        # Voice search
        'voice_search': "🎙 Голосовой поиск",
        'voice_search_button': "🎙 Голосовой поиск",
        'voice_search_prompt': "Отправьте голосовое сообщение с номером телефона или именем для поиска.",
        'voice_processing': "🔍 Обработка вашего голосового сообщения...",
        'voice_recognized': "Распознанный текст",
        'voice_not_understood': "❌ Извините, я не смог понять ваше голосовое сообщение. Пожалуйста, попробуйте снова.",
        'voice_api_error': "❌ Ошибка подключения к сервису распознавания речи",
        'voice_error': "❌ Ошибка обработки голосового сообщения",
        'voice_tips': "📝 <b>Советы по голосовому поиску:</b>\n\n• Четко произносите номер телефона: '993 65 123 45 67'\n• Говорите полное имя: 'Сердар Бердымухамедов'\n• Говорите в тихом месте\n• Держите микрофон ближе\n• Говорите медленно и четко",
        'voice_help': "🎙 <b>Помощь по голосовому поиску</b>\n\nДля поиска голосом:\n1. Нажмите и удерживайте кнопку микрофона\n2. Четко произнесите номер телефона или имя\n3. Отпустите кнопку\n\nПример: '993 65 123 45 67' или 'Сердар Бердымухамедов'",
        'notes_button': "📝 Заметки",

        # Security messages
        'too_fast': "⚠️ <b>Слишком быстрые запросы!</b>\n\nПожалуйста, подождите немного между поисками.",
        'unsafe_input': "⚠️ <b>Обнаружен потенциально опасный ввод.</b>\n\nПожалуйста, используйте только буквы, цифры и обычные символы.",
        'security_warning': "🔒 <b>Предупреждение безопасности</b>\n\nВ вашем аккаунте обнаружена подозрительная активность. Измените пароль для защиты вашего аккаунта.",
        'account_locked': "🔒 <b>Аккаунт временно заблокирован</b>\n\nИз-за нескольких подозрительных действий ваш аккаунт временно заблокирован в целях безопасности. Повторите попытку через 30 минут.",
        'security_check': "🔍 <b>Проверка безопасности</b>\n\nЭто действие требует проверки безопасности. Подтвердите свой номер телефона для подтверждения."
    },
    'en': {  # English
        'welcome': "Welcome to the TmM CELL Search system! 👀 \n\nThe service provides information by phone number, using open and public databases. \n\nThe service works in real-time and generates a report without storing all the received information. \n\nTechnical Support - @TMCELLadmin",
        'language_select': "Please select your language / Пожалуйста, выберите язык:",
        'phone_confirm': "🗂 You need to confirm your phone number to complete the identification and use the search.\n\nClick the button below to confirm.",
        'phone_button': "📲 Send phone number",
        'phone_success': "Your phone number has been successfully confirmed! Now you can use the search service.",
        'phone_verification_failed': "⚠️ Your phone number verification failed. Please try again.",
        'search_start_verification': "🔍 START SEARCH",
        'search_options': "📱 Select search type:\n\n📱 By phone number\n👤 By name\n📗 By passport",
        'search_form': "✅ Form for searching numbers starting with 993 and 63!\n\n🔍Free searches are used \n* You have {searches_left} free points left.\n\n⚠️ Numbers starting with +993 will not be searched!\n\n🙈 Enter your phone number for search:",
        'search_name_form': "✅ Form for searching by name!\n\n🔍Free searches are used \n* You have {searches_left} free points left.\n\n⚠️ Enter at least 3 characters!\n\n👤 Enter name to search:",
        'search_passport_form': "✅ Form for searching by passport!\n\n🔍Free searches are used \n* You have {searches_left} free points left.\n\n⚠️ Enter at least 4 characters!\n\n📗 Enter passport number:",
        'invalid_name_query': "⚠️ Please enter at least 3 characters to search by name.",
        'invalid_passport_query': "⚠️ Please enter at least 4 characters to search by passport.",
        'cancel': "❌️ CANCEL",
        'searching': "📡📡📡 Searching for information about number {phone_number}...",
        'search_results': "✅ Search completed!\n🔍 1 result found\n\n📱 Number:\n<code>{phone}</code>\n\n👤 Full Name:\n<code>{name}</code>\n\n🏠 Address:\n<code>{address}</code>\n\n📗 Passport:\n<code>{passport}</code>\n\n📅 Place and date of birth:\n<code>{birth_info}</code>\n\n🆔 SIM ID:\n<code>{sim_id}</code>\n\n🤖 Bot: @tmcell993bot",
        'no_results': "Sorry, no such number was found.",
        'limit_reached': "You have reached the request limit for today. 😓",
        'no_points': "You have run out of search points. 😓",
        'profile_title': "Welcome to your account {username} 👋",
        'profile_info': "🆔 ID: {user_id}\n📊 Total searches: {total_searches}\n👥 Invited users: {referrals}\n\n🔍 Search points: {search_points}\n* Each search costs 1 point.\n\nInvite users and get search points! For each invited user +1 search point.\n✅ Your referral link:\n{referral_link}",
        'profile_info_extended': "🆔 ID: {user_id}\n📊 Total searches: {total_searches}\n📱 Recent searches: {recent_searches}\n👥 Invited users: {referrals}\n\n📲 Phone number: {phone_number}\n📱 Phone verified: {phone_verified}\n\n👑 VIP Status: {vip_status}\n🔍 Search points: {search_points}\n* Each search costs 1 point.\n\nInvite users and get search points! For each invited user +1 search point.\n✅ Your referral link:\n{referral_link}",
        'vip_status_active': "✅ Active ({days} days remaining)",
        'vip_status_expired': "❌ Expired",
        'vip_status_active_no_expiry': "✅ Active (unlimited)",
        'vip_status_inactive': "❌ Inactive",
        'back': "⬅️ Back",
        'vip_title': "Paid VIP features of our bot:",
        'vip_tmcell': "📱 TMCELL (Payment)",
        'vip_dealer': "Select a dealer for payment via TMCELL:\n\nOfficial owners and administrators of TmM CELL Bot:",
        'vip_owner': "👤 BOT OWNER",
        'faq_title': "Frequently Asked Questions (FAQ)",
        'faq_content': (
            "<b>Baglan Gözleg Bot — Ulanyjy Kömek we Sorag-Jogaplar</b>\n\n"
            "<b>1. Botdan nähili peýdalanyp bilerin?</b>\n"
            "— Başlamak üçin /start ýa-da 'Gözleg' düwmesine basyň. Telefon belgiňizi tassyklap, gözleg görnüşini saýlaň.\n\n"
            "<b>2. Gözlegler nähili işleýär?</b>\n"
            "— Telefon belgisi, ady ýa-da passport boýunça maglumat gözläp bilersiňiz. Netije tapylanda maglumatlar ekranda görkeziler.\n\n"
            "<b>3. Gözleg çäklendirmeleri barmy?</b>\n"
            "— Her ulanyjy her gün 1 mugt gözleg edip biler. Köp gözleg üçin VIP ýa-da goşmaça ballar gerek.\n\n"
            "<b>4. Ýalňyşlyk ýa-da tehniki mesele ýüze çyksa näme etmeli?</b>\n"
            "— Ilki internetiňizi barlaň. Problemalar dowam etse, @baglan_support bilen habarlaşyň.\n\n"
            "<b>5. Netije tapylmasa näme etmeli?</b>\n"
            "— Eger maglumat tapylmasa, bu belgide maglumat ýoklugyny aňladýar. Başga maglumat ýa-da başga belgi synap görüň.\n\n"
            "<b>6. Profil we sazlamalar</b>\n"
            "— Profil bölümini açyp, maglumatlaryňyzy we sazlamalaryňyzy üýtgedip bilersiňiz.\n\n"
            "<b>7. VIP statusy näme berýär?</b>\n"
            "— VIP ulanyjylar çäksiz gözleg, öňdebaryjy hyzmat we aýratyn mümkinçilikler alýarlar.\n\n"
            "<b>8. Dostlarymy nädip çagyrsam bolýar?</b>\n"
            "— Profilde referal linkiňizi tapyp, dostlaryňyza paýlaşyň. Her çagyrylan ulanyjy üçin bonus bal alarsyňyz.\n\n"
            "<b>9. Maglumatlary nädip eksport edip bilerin?</b>\n"
            "— Gözleg taryhyňyzy eksport etmek üçin degişli düwmä basyň. CSV ýa-da QR görnüşinde alyp bilersiňiz.\n\n"
            "<b>10. Has giňişleýin kömek gerekmi?</b>\n"
            "— Islendik sorag üçin @baglan_support bilen habarlaşyň!\n"
        ),
        'change_language': "🇬🇧 Change language",
        'select_new_language': "Select a new language:",
        'search_button': "START SEARCH",
        'profile_button': "Personal Account",
        'profile': "Profile",
        'vip_button': "BUY VIP",
        'faq_button': "Questions",
        'language_button': "Change language",
        'filter_button': "Filter",
        'filter_title': "Select filters for search results:",
        'filter_by_city': "Filter by city",
        'filter_by_year': "Filter by birth year",
        'filter_by_name': "Filter by name",
        'filter_reset': "Reset filters",
        'interactive_view': "View details",
        'interactive_share': "Share",
        'interactive_save': "Add to favorites",
        'interactive_export': "Export",
        'export_caption': "📥 Search result exported",
        'city_filter_prompt': "Enter city (example: Ashgabat):",
        'year_filter_prompt': "Enter birth year (example: 1990):",
        'name_filter_prompt': "Enter name (example: Aman):",
        'history_button': "Search History",
        'favorites_button': "Favorites",
        'history_title': "Your search history:",
        'favorites_title': "Your favorite results:",
        'no_history': "Your search history is empty.",
        'no_favorites': "You don't have any favorite results.",
        'favorite_added': "Result added to favorites!",
        'favorite_exists': "This result is already in favorites.",
        'delete_favorite': "Remove from favorites",
        'clear_history': "Clear history",
        'combined_search': "Combined search",
        'combined_search_prompt': "Enter multiple parameters (example: name and passport number):",
        'theme_button': "Change theme",
        'theme_light': "Light theme",
        'theme_dark': "Dark theme",
        'theme_changed': "Theme changed",
        'current_theme': "Current theme",
        'theme_auto': "Sync with Telegram",
        'theme_auto_desc': "Theme automatically syncs with Telegram settings",
        'search_again': "To perform another search, click the button below:",
        'phone_verification_required': "You need to confirm your phone number to complete the identification and use the search.",
        'phone_verification_button': "Send phone number",
        'phone_verification_success': "Your phone number has been successfully verified! You can now use the tmcell number search service.",
        'settings_button': "Settings",
        'notifications_button': "Notifications",
        'support_button': "Technical Support",
        'commands_button': "Commands",
        'support_title': "Technical Support",
        'support_description': "For technical support, you can contact @TMCELLadmin.",
        'support_contact': "Contact",
        'notification_new': "🆕 New notification!",
        'notification_read': "Notification read",
        'notification_mark_all': "Mark all as read",
        'notification_none': "You have no notifications",
        'notification_unread': "Unread notifications: {count}",
        'notification_create': "Create notification",
        'notification_delete': "Delete notification",
        'notification_type_feature': "New feature",
        'notification_type_system': "System notification",
        'notification_type_promo': "Promo notification",
        'settings_title': "Settings",
        'language_setting': "Language",
        'theme_setting': "Theme",
        'notification_settings': "Notification Settings",
        'notifications_enabled': "Enabled",
        'notifications_disabled': "Disabled",
        'notification_status': "Notification Status",
        'enable_notifications': "Enable Notifications",
        'disable_notifications': "Disable Notifications",
        'notifications_now_enabled': "Notifications have been enabled",
        'notifications_now_disabled': "Notifications have been disabled",

        # Top users
        'top_points': "Top users by points",
        'top_referrals': "Top users by referrals",
        'refresh': "Refresh",

        # User profile
        'edit_profile': "Edit Profile",
        'search_preferences': "Search Preferences",
        'edit_name': "Edit Name",
        'edit_email': "Edit Email",
        'edit_phone': "Edit Phone",
        'edit_bio': "Edit Bio",
        'upload_photo': "Upload Photo",
        'enter_new_name': "Please enter your new name:",
        'enter_new_email': "Please enter your new email address:",
        'enter_new_phone': "Please enter your new phone number:",
        'enter_new_bio': "Please enter your new bio (short description about yourself):",
        'upload_new_photo': "Please send a photo to set as your profile picture.",
        'name_updated': "✅ Your name has been updated!",
        'email_updated': "✅ Your email has been updated!",
        'phone_updated': "✅ Your phone number has been updated!",
        'bio_updated': "✅ Your bio has been updated!",
        'photo_updated': "✅ Your profile photo has been updated!",
        'invalid_email': "❌ Invalid email format. Please try again.",
        'invalid_phone': "❌ Invalid phone number. Please enter a valid number.",
        'no_photo_found': "❌ No photo found. Please send a photo.",
        'not_set': "Not set",
        'edit_profile_text': "<b>✏️ Edit Profile</b>\n\nSelect what you want to edit:",

        # Search preferences
        'set_results_per_page': "Set Results Per Page",
        'set_sort_order': "Set Sort Order",
        'set_default_type': "Set Default Search Type",
        'select_results_per_page': "Select how many results to show per page:",
        'select_sort_order': "Select the sort order for search results:",
        'select_default_type': "Select the default search type:",
        'sort_newest': "Newest First",
        'sort_oldest': "Oldest First",
        'search_type_phone': "Phone Number",
        'search_type_name': "Name",
        'search_type_passport': "Passport",
        'results_per_page_updated': "Results per page updated!",
        'sort_order_updated': "Sort order updated!",
        'default_type_updated': "Default search type updated!",

        # Voice search
        'voice_search': "🎙 Voice Search",
        'voice_search_button': "🎙 Voice Search",
        'voice_search_prompt': "Send a voice message with a phone number or name to search.",
        'voice_processing': "🔍 Processing your voice message...",
        'voice_recognized': "Recognized text",
        'voice_not_understood': "❌ Sorry, I couldn't understand your voice message. Please try again.",
        'voice_api_error': "❌ Error connecting to speech recognition service",
        'voice_error': "❌ Error processing voice message",
        'voice_tips': "📝 <b>Voice Search Tips:</b>\n\n• Clearly pronounce the phone number: '993 65 123 45 67'\n• Say the full name: 'Serdar Berdimuhamedov'\n• Speak in a quiet place\n• Keep the microphone close\n• Speak slowly and clearly",
        'voice_help': "🎙 <b>Voice Search Help</b>\n\nTo search using voice:\n1. Press and hold the microphone button\n2. Clearly say the phone number or name\n3. Release the button\n\nExample: '993 65 123 45 67' or 'Serdar Berdimuhamedov'",
        'notes_button': "📝 Notes",

        # Security messages
        'too_fast': "⚠️ <b>Too fast requests!</b>\n\nPlease wait a moment between searches.",
        'unsafe_input': "⚠️ <b>Potentially harmful input detected.</b>\n\nPlease use only letters, numbers, and regular symbols.",
        'security_warning': "🔒 <b>Security Warning</b>\n\nSuspicious activity has been detected on your account. Change your password to protect your account.",
        'account_locked': "🔒 <b>Account temporarily locked</b>\n\nDue to several suspicious actions, your account has been temporarily locked for security purposes. Try again in 30 minutes.",
        'security_check': "🔍 <b>Security Check</b>\n\nThis action requires a security check. Verify your phone number to confirm."
    }
}

def get_message(key, language='tm'):
    """Get a message in the specified language"""
    if language not in MESSAGES:
        language = 'tm'  # Default to Turkmen

    return MESSAGES[language].get(key, MESSAGES['tm'].get(key, f"Message not found: {key}"))
