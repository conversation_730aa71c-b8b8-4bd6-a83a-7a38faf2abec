import logging
import os
import time
# Import telegram modules with try/except to avoid IDE warnings
try:
    from telegram import Update
    from telegram.ext import <PERSON><PERSON><PERSON>, Command<PERSON><PERSON><PERSON>, CallbackQueryHand<PERSON>, MessageHandler, Filters, CallbackContext
except ImportError:
    # Define placeholder classes for type hints
    class Update: pass
    class CallbackContext: pass
    class Updater: pass
    class CommandHandler: pass
    class CallbackQueryHandler: pass
    class MessageHandler: pass
    class Filters: pass

# Import security utilities
from utils.security import rate_limit, check_input_safety, sanitize_input, log_security_event, secure_database_connection
from config import TOKEN
from handlers.start import start_command
from handlers.language import language_callback
from handlers.search import search_callback, handle_search_query
from handlers.profile import profile_callback
from handlers.vip import vip_callback
from handlers.faq import faq_callback
from handlers.admin import admin_callback, handle_admin_commands, set_unlimited_points_for_admin
from handlers.set_vip_commands import handle_set_vip_commands
from handlers.set_vip_perm_command import handle_set_vip_perm_command
from handlers.admin_commands import admin_commands_callback, admin_commands_command
from handlers.admin_panel import admin_panel, register_admin_panel_handlers
from handlers.history import view_history_callback, view_favorites_callback, navigate_favorites_callback, delete_favorite_callback, confirm_delete_favorite_callback, clear_history_callback, confirm_clear_history_callback, change_theme_callback, set_theme_callback
from handlers.promo import promo_command
from handlers.notifications import notifications_command, notifications_callback, handle_create_notification, admin_notifications, delete_notification_callback
from handlers.support import support_command, support_callback
from handlers.settings import settings_callback, notification_settings_callback, toggle_notifications_callback
from handlers.reload import reload_command
from handlers.new_users import new_users_command, new_users_callback
from handlers.restart import restart_command
from handlers.ping import ping_command
from handlers.status import status_command
from handlers.export import export_history_command, export_history_callback
from handlers.bulk_search import bulk_search_command, handle_bulk_search_input, bulk_export_callback
from handlers.stats import stats_command, stats_callback
from handlers.search_suggestions import handle_suggestion_callback
from handlers.user_commands import user_commands_command, user_commands_callback, vip_commands_callback
from handlers.admin_commands import admin_commands_command, admin_commands_callback
from handlers.owner_commands import owner_commands_command
from handlers.help import help_command, help_callback
from handlers.history_commands import history_command, favorites_command
from handlers.language_command import language_command, change_language_command
from handlers.vip_command import vip_command
from handlers.search_command import search_command
from handlers.settings_command import settings_command
from handlers.filter_command import filter_command
# Notes functionality removed
# from handlers.notes_command import notes_command, notes_callback
from handlers.faq_command import faq_command
from handlers.theme import change_theme_callback, set_theme_callback
from handlers.profile_edit import (
    edit_profile_callback, edit_name_callback, edit_email_callback,
    edit_phone_callback, edit_bio_callback, upload_photo_callback,
    handle_name_input, handle_email_input, handle_phone_input,
    handle_bio_input, handle_photo_upload
)
from handlers.block_user import block_user_command, unblock_user_command
from handlers.backup import backup_command, restore_command
from handlers.clear_history import clear_history_command, confirm_clear_all_history_callback, cancel_clear_history_callback
from handlers.search_preferences import (
    search_preferences_callback, set_results_per_page_callback,
    set_sort_order_callback, set_default_type_callback,
    handle_results_per_page, handle_sort_order, handle_default_type
)
from handlers.profile import profile_callback
from handlers.user_profile import user_profile_command
from utils.db import Database
from utils.keyboards import get_main_menu_keyboard
from utils.languages import get_message

# Import new handlers
from handlers.admin_stats import admin_stats_command, admin_stats_callback
from handlers.export_history import export_history_command, export_history_callback
from handlers.share_result import share_result_callback
from handlers.language_manager import language_command, language_callback
from handlers.referral_manager import referral_command, referral_callback
from handlers.top_users import show_top_users_points, show_top_users_referrals

# Import new functionality
from handlers.auto_stats import setup_daily_stats_job, generate_daily_stats
from handlers.channel_notifications import setup_channel_notification_jobs
from handlers.auto_channel_posts import setup_auto_channel_posts
from handlers.search_history_analysis import analyze_search_history
from handlers.search_trends import get_search_trends
from handlers.user_recommendations import get_combined_recommendations
from handlers.search_history_command import search_history_analysis_command, search_trends_command

# Enable logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', level=logging.INFO
)
logger = logging.getLogger(__name__)

def error_handler(update: Update, context: CallbackContext):
    """Log errors caused by updates."""
    try:
        logger.error(f'Update {update} caused error {context.error}')

        # Send message to user if possible
        if update and update.effective_chat:
            context.bot.send_message(
                chat_id=update.effective_chat.id,
                text="An error occurred. Please try again later."
            )
    except Exception as e:
        logger.error(f'Error in error handler: {e}')

def main_menu_callback(update: Update, _: CallbackContext):
    """Handle main menu callback"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Show main menu
    query.edit_message_text(
        get_message('welcome', language),
        reply_markup=get_main_menu_keyboard(language, user_id)
    )

def check_data_files():
    """Check if the data files exist and create them if needed"""
    import os
    import json

    # Check if the phone data file exists
    if not os.path.exists("all_phone_data.json"):
        logger.error("Phone data file not found. Please make sure all_phone_data.json exists.")
        return False
    else:
        # Verify the file is valid and has data
        try:
            with open("all_phone_data.json", 'r', encoding='utf-8') as f:
                data = json.load(f)
                logger.info(f"Loaded phone data file with {len(data)} records.")
                if not data:
                    logger.error("Phone data file is empty.")
                    return False
        except Exception as e:
            logger.error(f"Error loading phone data file: {e}.")
            return False

    return True

# Matplotlib backend Agg (Tkinter errorlaryny düzetmek üçin)
import matplotlib
matplotlib.use('Agg')

from utils.phone_data import get_user_data_by_phone

def main():
    """Start the bot."""
    # Set up logging level for urllib3 to see more details
    try:
        import urllib3
        urllib3.disable_warnings()
        logging.getLogger('urllib3').setLevel(logging.WARNING)

        # Disable SSL verification
        try:
            # Directly disable SSL verification
            import ssl
            ssl._create_default_https_context = ssl._create_unverified_context
            logger.info("SSL verification disabled")
        except Exception as e:
            logger.error(f"Error disabling SSL verification: {e}")
    except ImportError:
        # urllib3 not available, continue without it
        logging.warning("urllib3 not available, continuing without it")

    # Initialize security features
    logger.info("Initializing security features...")
    try:
        # Secure database connection
        secure_database_connection()

        # Create security log file if it doesn't exist
        if not os.path.exists("security_logs.json"):
            with open("security_logs.json", "w") as f:
                f.write("[]")
            logger.info("Created security log file")

        # Log security initialization
        log_security_event("SYSTEM", "security_initialized", {
            "timestamp": time.time(),
            "version": "1.0"
        })

        logger.info("Security features initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing security features: {e}")

    # Check and prepare data files
    if not check_data_files():
        logger.error("Failed to prepare data files. Bot may not work correctly.")

    # Get custom API server if defined
    api_server = None
    try:
        from config import API_SERVER
        api_server = API_SERVER
        logger.info(f"Using custom API server: {API_SERVER}")
    except (ImportError, AttributeError):
        logger.info("Using default API server")

    # Check if proxy is enabled
    try:
        from proxy_config import USE_PROXY, PROXY_URL, PROXY_PORT, PROXY_USERNAME, PROXY_PASSWORD

        if USE_PROXY:
            # Configure proxy
            request_kwargs = {
                'proxy_url': f"{PROXY_URL}:{PROXY_PORT}",
                'read_timeout': 60,
                'connect_timeout': 60
            }

            # Add authentication if provided
            if PROXY_USERNAME and PROXY_PASSWORD:
                request_kwargs['urllib3_proxy_kwargs'] = {
                    'username': PROXY_USERNAME,
                    'password': PROXY_PASSWORD
                }

            logger.info(f"Using proxy: {PROXY_URL}:{PROXY_PORT}")

            # Create the Updater with proxy and possibly custom API server
            if api_server:
                updater = Updater(TOKEN, base_url=api_server, request_kwargs=request_kwargs)
            else:
                updater = Updater(TOKEN, request_kwargs=request_kwargs)
        else:
            # Create the Updater without proxy but possibly with custom API server
            if api_server:
                updater = Updater(TOKEN, base_url=api_server, request_kwargs={
                    'read_timeout': 60,
                    'connect_timeout': 60
                })
            else:
                updater = Updater(TOKEN, request_kwargs={
                    'read_timeout': 60,
                    'connect_timeout': 60
                })
    except ImportError:
        # If proxy_config.py doesn't exist, continue without proxy but possibly with custom API server
        logger.warning("proxy_config.py not found, continuing without proxy")
        if api_server:
            updater = Updater(TOKEN, base_url=api_server, request_kwargs={
                'read_timeout': 60,
                'connect_timeout': 60
            })
        else:
            updater = Updater(TOKEN, request_kwargs={
                'read_timeout': 60,
                'connect_timeout': 60
            })

    # Get the dispatcher to register handlers
    dp = updater.dispatcher

    # Command handlers
    dp.add_handler(CommandHandler("start", start_command))
    dp.add_handler(CommandHandler("ping", ping_command))
    dp.add_handler(CommandHandler("export", export_history_command))
    dp.add_handler(CommandHandler("bulk", bulk_search_command))
    dp.add_handler(CommandHandler("stats", stats_command))
    dp.add_handler(CommandHandler("profile", user_profile_command))
    dp.add_handler(CommandHandler("commands", user_commands_command))
    dp.add_handler(CommandHandler("help", help_command))
    dp.add_handler(CommandHandler("history", history_command))
    dp.add_handler(CommandHandler("favorites", favorites_command))
    dp.add_handler(CommandHandler("language", language_command))
    dp.add_handler(CommandHandler("vip", vip_command))
    dp.add_handler(CommandHandler("search", search_command))
    dp.add_handler(CommandHandler("settings", settings_command))
    dp.add_handler(CommandHandler("filter", filter_command))
    dp.add_handler(CommandHandler("faq", faq_command))
    dp.add_handler(CommandHandler("referral", referral_command))

    # Admin command handler
    dp.add_handler(CommandHandler("admin", admin_panel))
    dp.add_handler(CommandHandler("admin_stats", admin_stats_command))
    dp.add_handler(CommandHandler("add_points", handle_admin_commands))
    dp.add_handler(CommandHandler("remove_points", handle_admin_commands))
    dp.add_handler(CommandHandler("reload", reload_command))
    dp.add_handler(CommandHandler("restart", restart_command))
    dp.add_handler(CommandHandler("status", status_command))
    dp.add_handler(CommandHandler("view_stats", handle_admin_commands))
    dp.add_handler(CommandHandler("set_vip", handle_admin_commands))

    # VIP commands
    dp.add_handler(CommandHandler("set_vip_1", handle_set_vip_commands))
    dp.add_handler(CommandHandler("set_vip_3", handle_set_vip_commands))
    dp.add_handler(CommandHandler("set_vip_6", handle_set_vip_commands))
    dp.add_handler(CommandHandler("set_vip_perm", handle_set_vip_perm_command))

    # Admin commands
    dp.add_handler(CommandHandler("broadcast", handle_admin_commands))
    dp.add_handler(CommandHandler("promo", promo_command))
    dp.add_handler(CommandHandler("notifications", notifications_command))
    dp.add_handler(CommandHandler("create_notification", handle_create_notification))
    dp.add_handler(CommandHandler("support", support_command))
    dp.add_handler(CommandHandler("admin_commands", admin_commands_command))
    dp.add_handler(CommandHandler("owner_commands", owner_commands_command))
    dp.add_handler(CommandHandler("new_admin", handle_set_vip_commands))
    dp.add_handler(CommandHandler("block_user", block_user_command))
    dp.add_handler(CommandHandler("unblock_user", unblock_user_command))
    dp.add_handler(CommandHandler("backup", backup_command))
    dp.add_handler(CommandHandler("restore", restore_command))
    dp.add_handler(CommandHandler("clear_history", clear_history_command))
    dp.add_handler(CommandHandler("new_users", new_users_command))
    dp.add_handler(CommandHandler("search_analysis", search_history_analysis_command))
    dp.add_handler(CommandHandler("search_trends", search_trends_command))

    # Callback query handlers
    dp.add_handler(CallbackQueryHandler(language_callback, pattern='^lang_'))
    dp.add_handler(CallbackQueryHandler(change_language_command, pattern='^change_language$'))
    dp.add_handler(CallbackQueryHandler(new_users_callback, pattern='^new_users_[0-9]+$'))
    dp.add_handler(CallbackQueryHandler(search_callback, pattern='^search_'))
    dp.add_handler(CallbackQueryHandler(search_callback, pattern='^filter_'))
    dp.add_handler(CallbackQueryHandler(search_callback, pattern='^view_details_'))
    dp.add_handler(CallbackQueryHandler(share_result_callback, pattern='^share_result_'))
    dp.add_handler(CallbackQueryHandler(search_callback, pattern='^save_result_'))
    dp.add_handler(CallbackQueryHandler(search_callback, pattern='^copy_text_'))
    dp.add_handler(CallbackQueryHandler(search_callback, pattern='^copy_result_'))
    dp.add_handler(CallbackQueryHandler(search_callback, pattern='^export_result_'))
    dp.add_handler(CallbackQueryHandler(search_callback, pattern='^request_phone_verification$'))
    dp.add_handler(CallbackQueryHandler(profile_callback, pattern='^profile_'))
    dp.add_handler(CallbackQueryHandler(vip_callback, pattern='^vip_'))
    dp.add_handler(CallbackQueryHandler(faq_callback, pattern='^faq_view$'))
    dp.add_handler(CallbackQueryHandler(main_menu_callback, pattern='^main_menu$'))
    dp.add_handler(CallbackQueryHandler(admin_callback, pattern='^admin_'))
    dp.add_handler(CallbackQueryHandler(admin_commands_callback, pattern='^admin_commands$'))
    dp.add_handler(CallbackQueryHandler(admin_stats_callback, pattern='^admin_stats_'))

    # History and favorites handlers
    dp.add_handler(CallbackQueryHandler(view_history_callback, pattern='^view_history$'))
    dp.add_handler(CallbackQueryHandler(view_favorites_callback, pattern='^view_favorites$'))
    dp.add_handler(CallbackQueryHandler(navigate_favorites_callback, pattern='^(next|prev)_favorite_'))
    dp.add_handler(CallbackQueryHandler(delete_favorite_callback, pattern='^delete_favorite_'))
    dp.add_handler(CallbackQueryHandler(confirm_delete_favorite_callback, pattern='^confirm_delete_favorite_'))
    dp.add_handler(CallbackQueryHandler(confirm_delete_favorite_callback, pattern='^cancel_delete_favorite_'))
    dp.add_handler(CallbackQueryHandler(clear_history_callback, pattern='^clear_history$'))
    dp.add_handler(CallbackQueryHandler(confirm_clear_history_callback, pattern='^confirm_clear_history$'))
    dp.add_handler(CallbackQueryHandler(confirm_clear_history_callback, pattern='^cancel_clear_history$'))
    dp.add_handler(CallbackQueryHandler(confirm_clear_all_history_callback, pattern='^confirm_clear_all_history$'))
    dp.add_handler(CallbackQueryHandler(cancel_clear_history_callback, pattern='^cancel_clear_history$'))

    # Theme handlers
    from handlers.theme import get_effective_theme
    dp.add_handler(CallbackQueryHandler(change_theme_callback, pattern='^change_theme$'))
    dp.add_handler(CallbackQueryHandler(set_theme_callback, pattern='^set_theme_(light|dark|auto)$'))

    # Notification handlers
    dp.add_handler(CallbackQueryHandler(notifications_callback, pattern='^view_notifications$'))
    dp.add_handler(CallbackQueryHandler(notifications_callback, pattern='^notification_[0-9]+$'))
    dp.add_handler(CallbackQueryHandler(notifications_callback, pattern='^mark_all_read$'))
    dp.add_handler(CallbackQueryHandler(notifications_callback, pattern='^create_notification$'))
    dp.add_handler(CallbackQueryHandler(notifications_callback, pattern='^disable_notifications$'))
    dp.add_handler(CallbackQueryHandler(admin_notifications, pattern='^admin_notifications$'))
    dp.add_handler(CallbackQueryHandler(delete_notification_callback, pattern='^delete_notification_[0-9]+$'))

    # Support handler
    dp.add_handler(CallbackQueryHandler(support_callback, pattern='^support$'))

    # Settings handlers
    dp.add_handler(CallbackQueryHandler(settings_callback, pattern='^profile_settings$'))
    dp.add_handler(CallbackQueryHandler(settings_callback, pattern='^settings$'))
    dp.add_handler(CallbackQueryHandler(notification_settings_callback, pattern='^notification_settings$'))
    dp.add_handler(CallbackQueryHandler(toggle_notifications_callback, pattern='^(enable|disable)_notifications$'))

    # Export handlers
    dp.add_handler(CallbackQueryHandler(export_history_callback, pattern='^export_(txt|csv|excel|pdf)$'))

    # Bulk search handlers
    dp.add_handler(CallbackQueryHandler(bulk_export_callback, pattern='^bulk_export_(txt|csv)$'))

    # Stats handlers
    dp.add_handler(CallbackQueryHandler(stats_callback, pattern='^stats_(users|searches|vip|graph|menu)$'))

    # Top users handlers
    dp.add_handler(CallbackQueryHandler(show_top_users_points, pattern='^top_points$'))
    dp.add_handler(CallbackQueryHandler(show_top_users_referrals, pattern='^top_referrals$'))

    # Profile handlers
    dp.add_handler(CallbackQueryHandler(profile_callback, pattern='^profile_view$'))

    # Search suggestion handlers
    dp.add_handler(CallbackQueryHandler(handle_suggestion_callback, pattern='^suggest_'))

    # User commands handlers
    dp.add_handler(CallbackQueryHandler(user_commands_callback, pattern='^user_commands$'))
    dp.add_handler(CallbackQueryHandler(vip_commands_callback, pattern='^vip_commands$'))

    # Admin commands handlers
    dp.add_handler(CommandHandler('admin_commands', admin_commands_command))
    dp.add_handler(CallbackQueryHandler(admin_commands_callback, pattern='^admin_commands$'))

    # Search stats handler
    from handlers.search_stats import search_stats_command
    dp.add_handler(CommandHandler('search_stats', search_stats_command))

    # Help handlers
    dp.add_handler(CallbackQueryHandler(help_callback, pattern='^help$'))

    # Referral handlers
    dp.add_handler(CallbackQueryHandler(referral_callback, pattern='^referral_'))

    # Profile edit handlers
    dp.add_handler(CallbackQueryHandler(edit_profile_callback, pattern='^edit_profile$'))
    dp.add_handler(CallbackQueryHandler(edit_name_callback, pattern='^edit_name$'))
    dp.add_handler(CallbackQueryHandler(edit_email_callback, pattern='^edit_email$'))
    dp.add_handler(CallbackQueryHandler(edit_phone_callback, pattern='^edit_phone$'))
    dp.add_handler(CallbackQueryHandler(edit_bio_callback, pattern='^edit_bio$'))
    dp.add_handler(CallbackQueryHandler(upload_photo_callback, pattern='^upload_photo$'))

    # Search preferences handlers
    dp.add_handler(CallbackQueryHandler(search_preferences_callback, pattern='^search_preferences$'))
    dp.add_handler(CallbackQueryHandler(set_results_per_page_callback, pattern='^set_results_per_page$'))
    dp.add_handler(CallbackQueryHandler(set_sort_order_callback, pattern='^set_sort_order$'))
    dp.add_handler(CallbackQueryHandler(set_default_type_callback, pattern='^set_default_type$'))
    dp.add_handler(CallbackQueryHandler(handle_results_per_page, pattern='^results_per_page_[0-9]+$'))
    dp.add_handler(CallbackQueryHandler(handle_sort_order, pattern='^sort_order_(asc|desc)$'))
    dp.add_handler(CallbackQueryHandler(handle_default_type, pattern='^default_type_(phone|name|passport)$'))

    # Message handlers for text input
    dp.add_handler(MessageHandler(
        Filters.text & ~Filters.command,
        lambda update, context: handle_bulk_search_input(update, context) if context.user_data.get('awaiting_bulk_search')
        else handle_name_input(update, context) if context.user_data.get('awaiting_name_input')
        else handle_email_input(update, context) if context.user_data.get('awaiting_email_input')
        else handle_phone_input(update, context) if context.user_data.get('awaiting_phone_input')
        else handle_bio_input(update, context) if context.user_data.get('awaiting_bio_input')
        else handle_create_notification(update, context) if context.user_data.get('awaiting_notification_creation')
        else handle_search_query(update, context)
    ))

    # Phone verification handlers
    from handlers.phone_verification import handle_contact_message, handle_phone_verification_callback
    dp.add_handler(MessageHandler(
        Filters.contact,
        lambda update, context: handle_contact_message(update, context)
    ))
    dp.add_handler(CallbackQueryHandler(handle_phone_verification_callback, pattern='^request_phone_verification$'))

    # Message handler for photo uploads
    dp.add_handler(MessageHandler(
        Filters.photo,
        lambda update, context: handle_photo_upload(update, context) if context.user_data.get('awaiting_photo_upload') else None
    ))

    # Error handler
    dp.add_error_handler(error_handler)

    # Reset daily search limits at midnight
    # This would typically be done with a scheduler like APScheduler
    # For simplicity, we'll just reset them on bot startup
    db = Database()
    db.reset_daily_searches()
    logger.info("Daily search limits reset")

    # Check and update expired VIP statuses
    expired_count = db.check_vip_status()
    logger.info(f"Checked VIP status expiry: {expired_count} users updated")

    # Run the daily points check once at startup
    import daily_points_scheduler
    daily_points_scheduler.run_daily_points_check()
    logger.info("Daily points check completed")

    # Set unlimited points for admin users
    set_unlimited_points_for_admin()
    logger.info("Set unlimited points for admin users")

    # Set owner as permanent VIP
    from config import OWNER_ID

    # Check if owner exists in database
    if not db.user_exists(OWNER_ID):
        # Add owner to database
        db.add_user(OWNER_ID, "owner", "7772025660")

    # Set owner as permanent VIP
    db.set_user_vip_status(OWNER_ID, True, 36500, permanent=True)  # 100 years (permanent)

    # Give owner unlimited search points
    db.cur.execute("""
        UPDATE users
        SET search_points = 999999
        WHERE user_id = ?
    """, (OWNER_ID,))
    db.conn.commit()

    # Log this action
    logger.info(f"Owner (ID: {OWNER_ID}) set as permanent VIP with unlimited search points")

    # Register admin panel handlers
    register_admin_panel_handlers(dp)
    logger.info("Admin panel handlers registered")

    # Set up daily stats job
    setup_daily_stats_job(updater.job_queue)
    logger.info("Daily stats job scheduled")

    # Set up channel notification jobs
    setup_channel_notification_jobs(updater.job_queue)
    logger.info("Channel notification jobs scheduled")
    
    # Set up automatic channel posts
    setup_auto_channel_posts(updater.job_queue)
    logger.info("Automatic channel posts scheduled")

    # Generate initial stats
    generate_daily_stats(None)
    logger.info("Initial stats generated")

    # Start the Bot
    updater.start_polling()
    logger.info("Bot started")

    # Run the bot until you press Ctrl-C
    updater.idle()

if __name__ == '__main__':
    main()