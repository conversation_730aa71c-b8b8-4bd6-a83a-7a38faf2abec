import sqlite3
from config import SQLITE_DB_PATH

def create_database():
    conn = sqlite3.connect(SQLITE_DB_PATH)
    cur = conn.cursor()
    
    # Numbers table döredýäris
    cur.execute("""
    CREATE TABLE IF NOT EXISTS numbers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        phone_number TEXT NOT NULL,
        full_name TEXT NOT NULL,
        address TEXT,
        passport TEXT,
        birth_info TEXT,
        sim_id TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    """)
    
    # Users table döredýäris
    cur.execute("""
    CREATE TABLE IF NOT EXISTS users (
        user_id INTEGER PRIMARY KEY,
        language TEXT DEFAULT 'tm',
        is_vip BOOLEAN DEFAULT FALSE,
        searches_today INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    """)
    
    # Searches table döredýäris
    cur.execute("""
    CREATE TABLE IF NOT EXISTS searches (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        search_query TEXT,
        search_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (user_id)
    )
    """)
    
    conn.commit()
    conn.close()

if __name__ == "__main__":
    create_database()
    print("Database successfully created!")