from telegram import InlineKeyboardButton, InlineKeyboardMarkup, KeyboardButton, ReplyKeyboardMarkup
from utils.languages import get_message
from config import ADMIN_IDS

def get_language_keyboard():
    """Returns a keyboard with language selection buttons"""
    keyboard = [
        [InlineKeyboardButton("🇬🇧 English", callback_data="lang_en"),
         InlineKeyboardButton("🇷🇺 Русский", callback_data="lang_ru")],
        [InlineKeyboardButton("🇹🇲 Türkmen", callback_data="lang_tm")]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_main_menu_keyboard(language='tm', user_id=None):
    """Returns the main menu keyboard with admin button if user is admin"""
    from utils.db import Database

    # Always get the latest total users count from the database
    db = Database()
    total_users = db.get_total_users_count()
    unread_count = 0
    if user_id:
        unread_count = db.get_unread_notifications_count(user_id)

    # Create notification button text with unread count if any
    notification_text = get_message('notifications_button', language)
    if unread_count > 0:
        notification_text += f" ({unread_count})"

    # Create search button with appropriate icon and total users count
    if language == 'ru':
        search_button_text = f"🔍 Поиск | 👥 {total_users}"
    elif language == 'en':
        search_button_text = f"🔍 Search | 👥 {total_users}"
    else:
        search_button_text = f"🔍 Gözleg | 👥 {total_users}"

    keyboard = [
        [InlineKeyboardButton(search_button_text, callback_data="search_start")],
    ]
    keyboard.append([
        InlineKeyboardButton("💰 TOP 100 " + get_message('top_points', language), callback_data="top_points"),
        InlineKeyboardButton("👥 TOP 100 " + get_message('top_referrals', language), callback_data="top_referrals")
    ])
    keyboard.append([
        InlineKeyboardButton("👤 " + get_message('profile_button', language), callback_data="profile_view"),
        InlineKeyboardButton("⭐ " + get_message('vip_button', language), callback_data="vip_buy")
    ])
    keyboard.append([
        InlineKeyboardButton("📜 " + get_message('history_button', language), callback_data="view_history"),
        InlineKeyboardButton("💾 " + get_message('favorites_button', language), callback_data="view_favorites")
    ])
    keyboard.append([
        InlineKeyboardButton("📬 " + notification_text, callback_data="view_notifications"),
        InlineKeyboardButton("🌐 " + get_message('language_button', language), callback_data="change_language")
    ])
    keyboard.append([
        InlineKeyboardButton("⚙️ " + get_message('settings_button', language), callback_data="settings"),
        InlineKeyboardButton("❓ " + get_message('faq_button', language), callback_data="faq_view")
    ])
    keyboard.append([
        InlineKeyboardButton("🆘 " + get_message('support_button', language), callback_data="support"),
        InlineKeyboardButton("📋 " + get_message('commands_button', language), callback_data="user_commands")
    ])
    if user_id and user_id in ADMIN_IDS:
        from config import OWNER_ID
        if user_id == OWNER_ID:
            keyboard.append([InlineKeyboardButton("👑 Bot Eýesi", callback_data="admin_panel")])
        else:
            keyboard.append([InlineKeyboardButton("👑 Admin Panel", callback_data="admin_panel")])
    return InlineKeyboardMarkup(keyboard)

def get_phone_request_keyboard(language='tm'):
    """Returns a keyboard to request phone number"""
    keyboard = [[KeyboardButton(get_message('phone_button', language), request_contact=True)]]
    return ReplyKeyboardMarkup(keyboard, one_time_keyboard=True, resize_keyboard=True)

def get_search_verification_keyboard(language='tm'):
    """Returns a keyboard for search with phone verification button"""
    keyboard = [
        [InlineKeyboardButton(get_message('phone_verification_button', language), callback_data="request_phone_verification")]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_back_button(callback_data="main_menu", language='tm'):
    """Returns a back button"""
    return InlineKeyboardMarkup([[InlineKeyboardButton(get_message('back', language), callback_data=callback_data)]])

def get_search_cancel_keyboard(language='tm'):
    """Returns a keyboard with cancel button for search"""
    keyboard = [[InlineKeyboardButton(get_message('cancel', language), callback_data="search_cancel")]]
    return InlineKeyboardMarkup(keyboard)

def get_vip_keyboard(language='tm'):
    """Returns a keyboard for VIP purchase options"""
    if language == 'ru':
        keyboard = [
            [InlineKeyboardButton("💳 1 Месяц - 50 TMT (100 баллов)", callback_data="vip_1month")],
            [InlineKeyboardButton("💰 3 Месяца - 120 TMT (300 баллов)", callback_data="vip_3months")],
            [InlineKeyboardButton("💸 6 Месяцев - 200 TMT (1000 баллов)", callback_data="vip_6months")],
            [InlineKeyboardButton(get_message('vip_tmcell', language), callback_data="vip_tmcell")],
            [InlineKeyboardButton(get_message('back', language), callback_data="main_menu")]
        ]
    elif language == 'en':
        keyboard = [
            [InlineKeyboardButton("💳 1 Month - 50 TMT (100 points)", callback_data="vip_1month")],
            [InlineKeyboardButton("💰 3 Months - 120 TMT (300 points)", callback_data="vip_3months")],
            [InlineKeyboardButton("💸 6 Months - 200 TMT (1000 points)", callback_data="vip_6months")],
            [InlineKeyboardButton(get_message('vip_tmcell', language), callback_data="vip_tmcell")],
            [InlineKeyboardButton(get_message('back', language), callback_data="main_menu")]
        ]
    else:
        keyboard = [
            [InlineKeyboardButton("💳 1 Aý - 50 TMT (100 bal)", callback_data="vip_1month")],
            [InlineKeyboardButton("💰 3 Aý - 120 TMT (300 bal)", callback_data="vip_3months")],
            [InlineKeyboardButton("💸 6 Aý - 200 TMT (1000 bal)", callback_data="vip_6months")],
            [InlineKeyboardButton(get_message('vip_tmcell', language), callback_data="vip_tmcell")],
            [InlineKeyboardButton(get_message('back', language), callback_data="main_menu")]
        ]
    return InlineKeyboardMarkup(keyboard)

def get_vip_dealer_keyboard(language='tm'):
    """Returns a keyboard for VIP dealer selection"""
    keyboard = [
        [InlineKeyboardButton("👤 @TMCELLadmin (Owner)", url="https://t.me/TMCELLadmin")],
        [InlineKeyboardButton("👤 @Arslan_Vpns (Admin)", url="https://t.me/Arslan_Vpns")],
        [InlineKeyboardButton("👤 @rnxGG (Admin)", url="https://t.me/rnxGG")],
        [InlineKeyboardButton("👤 @nerwa_degme (Admin)", url="https://t.me/nerwa_degme")],
        [InlineKeyboardButton(get_message('back', language), callback_data="vip_buy")]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_subscription_keyboard(language='tm'):
    """Returns a keyboard for VIP purchase when points are exhausted"""
    keyboard = [
        [InlineKeyboardButton(get_message('vip_button', language), callback_data="vip_buy")],
        [InlineKeyboardButton(get_message('back', language), callback_data="main_menu")]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_new_search_keyboard(language='tm'):
    """Returns a keyboard for starting a new search"""
    keyboard = [
        [InlineKeyboardButton("🔎 " + get_message('search_button', language), callback_data="search_start")],
        [InlineKeyboardButton("🔍 " + get_message('filter_button', language), callback_data="filter_results")],
        [InlineKeyboardButton("🔙 " + get_message('back', language), callback_data="main_menu")]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_search_keyboard(language='tm'):
    """Returns the search menu keyboard"""
    # Create search options with appropriate text based on language
    if language == 'ru':
        phone_text = "📱 Поиск по номеру"
        name_text = "👤 Поиск по имени"
        passport_text = "📗 Поиск по паспорту"
        filter_text = "🔎 Фильтры поиска"
    elif language == 'en':
        phone_text = "📱 Search by phone"
        name_text = "👤 Search by name"
        passport_text = "📗 Search by passport"
        filter_text = "🔎 Search filters"
    else:
        phone_text = "📱 Telefon belgisi boýunça"
        name_text = "👤 Ady boýunça"
        passport_text = "📗 Passport boýunça"
        filter_text = "🔎 Gözleg filtrleri"

    keyboard = [
        [InlineKeyboardButton(phone_text, callback_data="search_number")],
        [InlineKeyboardButton(name_text, callback_data="search_name")],
        [InlineKeyboardButton(passport_text, callback_data="search_passport")],
        [InlineKeyboardButton(filter_text, callback_data="filter_results")],
        [InlineKeyboardButton("🔙 " + get_message('back', language), callback_data="main_menu")]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_filter_keyboard(language='tm'):
    """Returns a keyboard for filtering search results"""
    keyboard = [
        [InlineKeyboardButton(get_message('filter_by_city', language), callback_data="filter_city")],
        [InlineKeyboardButton(get_message('filter_by_year', language), callback_data="filter_year")],
        [InlineKeyboardButton(get_message('filter_by_name', language), callback_data="filter_name")],
        [InlineKeyboardButton(get_message('filter_reset', language), callback_data="filter_reset")],
        [InlineKeyboardButton(get_message('back', language), callback_data="search_start")]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_interactive_result_keyboard(language='tm', result_id=0):
    """Returns a keyboard for interactive search result actions"""
    # Create button texts based on language
    if language == 'ru':
        share_text = "📤 Поделиться"
        save_text = "⭐ Сохранить"
        export_text = "📥 Экспорт"
        copy_text = "📋 Копировать"
    elif language == 'en':
        share_text = "📤 Share"
        save_text = "⭐ Save"
        export_text = "📥 Export"
        copy_text = "📋 Copy"
    else:
        share_text = "📤 Paýlaşmak"
        save_text = "⭐ Ýatda saklamak"
        export_text = "📥 Eksport"
        copy_text = "📋 Göçürmek"

    # Simplified keyboard without detailed view and notes buttons
    keyboard = [
        [InlineKeyboardButton(share_text, callback_data=f"share_result_{result_id}"),
         InlineKeyboardButton(save_text, callback_data=f"save_result_{result_id}")],
        [InlineKeyboardButton(copy_text, callback_data=f"copy_result_{result_id}"),
         InlineKeyboardButton(export_text, callback_data=f"export_result_{result_id}")],
        [InlineKeyboardButton("🔍 " + get_message('new_search', language), callback_data="search_start"),
         InlineKeyboardButton("🔙 " + get_message('back', language), callback_data="main_menu")]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_history_keyboard(language='tm'):
    """Returns a keyboard for search history view"""
    keyboard = [
        [InlineKeyboardButton(get_message('clear_history', language), callback_data="clear_history")],
        [InlineKeyboardButton(get_message('back', language), callback_data="main_menu")]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_favorites_keyboard(language='tm'):
    """Returns a keyboard for favorites view"""
    keyboard = [
        [InlineKeyboardButton(get_message('delete_favorite', language), callback_data="delete_favorite")],
        [InlineKeyboardButton(get_message('back', language), callback_data="main_menu")]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_combined_search_keyboard(language='tm'):
    """Returns a keyboard for combined search"""
    keyboard = [
        [InlineKeyboardButton(get_message('combined_search', language), callback_data="combined_search")],
        [InlineKeyboardButton(get_message('back', language), callback_data="search_start")]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_theme_keyboard(language='tm'):
    """Returns a keyboard for theme selection"""
    keyboard = [
        [InlineKeyboardButton(get_message('theme_light', language), callback_data="theme_light")],
        [InlineKeyboardButton(get_message('theme_dark', language), callback_data="theme_dark")],
        [InlineKeyboardButton(get_message('back', language), callback_data="profile_view")]
    ]
    return InlineKeyboardMarkup(keyboard)